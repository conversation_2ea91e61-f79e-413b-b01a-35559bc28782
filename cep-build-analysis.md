# CEP Build Issue Analysis & Solution

## Problem Analysis

The current build is creating an `index.html` with inlined minified content instead of separate JavaScript files because of the **`vite-plugin-singlefile`** plugin in your Vite configuration.

### Root Cause
```typescript
// In vite.config.ts
import { viteSingleFile } from 'vite-plugin-singlefile';

export default defineConfig({
  plugins: [react(), viteSingleFile()], // ← This plugin inlines everything
  // ...
});
```

The `vite-plugin-singlefile` plugin is specifically designed to:
- Inline all CSS, JavaScript, and assets into a single HTML file
- Remove external file dependencies
- Create a self-contained HTML document

This approach works for some use cases but conflicts with CEP's expected structure where:
- JavaScript should be in separate `.js` files
- CSS should be in separate `.css` files
- Assets should be properly referenced, not inlined

### Additional Issues Identified

1. **Conflicting Rollup Options**: The `rollupOptions.output.entryFileNames` and `assetFileNames` configurations are being overridden by the singlefile plugin
2. **Asset Inlining**: `assetsInlineLimit: 0` tries to inline all assets, which conflicts with CEP's file structure expectations
3. **External Dependencies**: The external function in rollupOptions may not work as expected with the singlefile plugin

## Solution

### Option 1: Remove vite-plugin-singlefile (Recommended)

Replace your `vite.config.ts` with this optimized version:

```typescript
// vite.config.ts (CEP-optimized without singlefile plugin)
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { resolve } from 'path';

export default defineConfig({
  plugins: [react()], // Removed viteSingleFile plugin
  base: './', // Relative paths for CEP
  root: 'client', // source folder
  build: {
    target: 'es2020', // CEP Chromium compatibility
    outDir: resolve(__dirname, 'dist'),
    emptyOutDir: true,
    assetsInlineLimit: 4096, // Only inline small assets (4KB limit)
    chunkSizeWarningLimit: 600,
    rollupOptions: {
      output: {
        entryFileNames: 'assets/[name]-[hash].js', // Separate JS files
        chunkFileNames: 'assets/[name]-[hash].js',
        assetFileNames: (assetInfo) => {
          // Keep CSS files separate
          if (assetInfo.name?.endsWith('.css')) {
            return 'assets/[name]-[hash].css';
          }
          return 'assets/[name]-[hash].[ext]';
        },
        // Split chunks for better organization
        manualChunks: {
          vendor: ['react', 'react-dom'],
          shiki: ['shiki'],
          ui: ['lucide-react', 'zustand']
        }
      },
      external: (id) => {
        // Exclude CSInterface.js from bundling
        if (id.includes('CSInterface.js')) {
          return true;
        }

        // Handle Shiki language optimization
        const essentialLangs = [
          'javascript', 'typescript', 'jsx', 'tsx',
          'json', 'xml', 'markdown', 'shell'
        ];

        if (id.includes('@shikijs/langs/dist/') && id.endsWith('.mjs')) {
          const langName = id.split('/').pop()?.replace('.mjs', '');
          return !essentialLangs.includes(langName || '');
        }

        return false;
      }
    },
  },
  server: {
    port: 3000,
    strictPort: true
  },
});
```

### Option 2: Conditional Build Configuration

If you need the singlefile approach for some environments, use conditional configuration:

```typescript
// vite.config.ts (conditional approach)
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { viteSingleFile } from 'vite-plugin-singlefile';
import { resolve } from 'path';

const isCEPBuild = process.env.BUILD_TARGET === 'cep' || process.env.NODE_ENV === 'production';

export default defineConfig({
  plugins: [
    react(),
    // Only use singlefile for non-CEP builds
    ...(!isCEPBuild ? [viteSingleFile()] : [])
  ],
  base: './',
  root: 'client',
  build: {
    target: 'es2020',
    outDir: resolve(__dirname, 'dist'),
    emptyOutDir: true,
    assetsInlineLimit: isCEPBuild ? 4096 : 0,
    chunkSizeWarningLimit: 600,
    rollupOptions: {
      output: isCEPBuild ? {
        entryFileNames: 'assets/[name]-[hash].js',
        chunkFileNames: 'assets/[name]-[hash].js',
        assetFileNames: 'assets/[name]-[hash].[ext]',
        manualChunks: {
          vendor: ['react', 'react-dom'],
          shiki: ['shiki']
        }
      } : undefined,
      external: (id) => {
        if (id.includes('CSInterface.js')) return true;
        
        const essentialLangs = [
          'javascript', 'typescript', 'jsx', 'tsx',
          'json', 'xml', 'markdown', 'shell'
        ];

        if (id.includes('@shikijs/langs/dist/') && id.endsWith('.mjs')) {
          const langName = id.split('/').pop()?.replace('.mjs', '');
          return !essentialLangs.includes(langName || '');
        }

        return false;
      }
    },
  },
  server: {
    port: 3000,
    strictPort: true
  },
});
```

### Update Package.json Scripts

Update your build script to ensure proper CEP building:

```json
{
  "scripts": {
    "dev": "vite",
    "build": "BUILD_TARGET=cep vite build && node copy-cep-files.js",
    "build:single": "vite build",
    "preview": "vite preview",
    "lint": "eslint . --ext ts,tsx,js,jsx --report-unused-disable-directives",
    "lint:fix": "eslint . --ext ts,tsx,js,jsx --fix"
  }
}
```

### Update copy-cep-files.js

Enhance the copy script to handle the new build structure:

```javascript
// Add this section after copying files
async function updateHTMLForCEP() {
  const htmlPath = path.join(DIST, 'index.html');
  if (fs.existsSync(htmlPath)) {
    let htmlContent = await fs.promises.readFile(htmlPath, 'utf8');

    // Ensure CSInterface.js is loaded first
    if (!htmlContent.includes('<script src="./CSInterface.js">')) {
      htmlContent = htmlContent.replace(
        '<head>',
        '<head>\n  <script src="./CSInterface.js"></script>'
      );
    }

    // Fix asset paths to be relative
    htmlContent = htmlContent.replace(/href="\/assets\//g, 'href="./assets/');
    htmlContent = htmlContent.replace(/src="\/assets\//g, 'src="./assets/');

    await fs.promises.writeFile(htmlPath, htmlContent, 'utf8');
    console.log('  ✅ updated HTML for CEP compatibility');
  }
}

// Call this function in your main() function
await updateHTMLForCEP();
```

## Expected Result

After implementing the solution, your `dist` folder should contain:

```
dist/
├── index.html                 # Main HTML file with proper script/css references
├── CSInterface.js            # CEP API (copied from client/)
├── assets/
│   ├── index-[hash].js       # Main application bundle
│   ├── vendor-[hash].js      # React/React-DOM bundle
│   ├── shiki-[hash].js       # Shiki syntax highlighting
│   ├── ui-[hash].js          # UI components bundle
│   ├── index-[hash].css      # Compiled CSS
│   └── onig-[hash].wasm      # Shiki WASM file
├── CSXS/
│   └── manifest.xml
├── host/
│   └── ae-integration.jsxinc
└── icons/
    ├── icon-16.png
    └── icon-32.png
```

This structure follows CEP conventions with separate, minified JavaScript files while maintaining all the functionality of your React application.